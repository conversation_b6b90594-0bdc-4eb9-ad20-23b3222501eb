"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(pagebuilder)/content-builder/[...slug]/layout",{

/***/ "(app-pages-browser)/./src/layouts/builder/page/LayerSidebarLayout.tsx":
/*!*********************************************************!*\
  !*** ./src/layouts/builder/page/LayerSidebarLayout.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LayerSidebarLayout: function() { return /* binding */ LayerSidebarLayout; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Builder__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/Builder */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/FieldEditor.tsx\");\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pagebuilderlayout.module.scss */ \"(app-pages-browser)/./src/layouts/builder/page/pagebuilderlayout.module.scss\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n// Custom hook để phát hiện click bên ngoài một phần tử\nvar useClickOutside = function(callback) {\n    _s();\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        var handleClickOutside = function(event) {\n            if (ref.current && !ref.current.contains(event.target)) {\n                callback();\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return function() {\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        callback\n    ]);\n    return ref;\n};\n_s(useClickOutside, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\nvar LayerSidebarLayout = function() {\n    _s1();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__.PageBuilderContext);\n    var childCmp = context.childComponentData, setChildComponentData = context.setChildComponentData, layerPos = context.layerPos;\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0), 2), curChildIndex = _useState[0], setCurChildIndex = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), 2), isPrevEntriesOpen = _useState1[0], setIsPrevEntriesOpen = _useState1[1];\n    // Sử dụng hook useClickOutside để đóng dropdown khi click ra ngoài\n    var prevEntriesRef = useClickOutside(function() {\n        if (isPrevEntriesOpen) {\n            setIsPrevEntriesOpen(false);\n        }\n    });\n    var handleBack = function(idx) {\n        var newVal = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__._)(childCmp);\n        console.log(idx);\n        if (idx !== undefined) {\n            if (curChildIndex === idx) return;\n            setCurChildIndex(idx);\n            newVal.splice(idx + 1, newVal.length - (idx + 1));\n        } else {\n            setCurChildIndex(curChildIndex - 1);\n            newVal.pop();\n        }\n        setChildComponentData(newVal);\n    };\n    var handleRemove = function(idx) {\n        console.log(\"Remove idx:\", idx);\n        if (!childCmp[idx]) return;\n        var currentEntry = childCmp[idx];\n        // Use the handleRemove function passed from the Component\n        if (currentEntry.handleRemove && typeof currentEntry.entryIndex === \"number\") {\n            // Call the Component's handleRemove with the correct index\n            currentEntry.handleRemove(currentEntry.entryIndex);\n        }\n        // Update childComponentData - remove current and subsequent entries\n        var newChildData = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__._)(childCmp);\n        newChildData.splice(idx, newChildData.length - idx);\n        setChildComponentData(newChildData);\n        // Update current index to previous level\n        if (idx > 0) {\n            setCurChildIndex(idx - 1);\n        }\n    };\n    var handleDuplicate = function(idx) {\n        console.log(\"Duplicate idx:\", idx);\n        if (!childCmp[idx]) return;\n        var currentEntry = childCmp[idx];\n        // Use the handleDuplicate function passed from the Component\n        if (currentEntry.handleDuplicate && typeof currentEntry.entryIndex === \"number\") {\n            // Call the Component's handleDuplicate with the correct index\n            currentEntry.handleDuplicate(currentEntry.entryIndex);\n        }\n    // Note: Non-repeatable components cannot be duplicated\n    };\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__.useIsomorphicLayoutEffect)(function() {\n        if (!childCmp || childCmp.length === 0) return;\n        setCurChildIndex(childCmp.length - 1);\n    }, [\n        childCmp\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: childCmp && childCmp.length > 0 && childCmp[curChildIndex] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().sidebar), (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().sidebar__layer), layerPos !== \"\" ? (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default())[layerPos] : \"\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__title),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: function() {\n                                return handleBack(undefined);\n                            },\n                            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().back__button),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                type: \"cms\",\n                                variant: \"back\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 8\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 7\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                            className: \"collect__heading collect__heading--h6\",\n                            children: [\n                                childCmp.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries),\n                                            ref: prevEntriesRef,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: function() {\n                                                        return setIsPrevEntriesOpen(!isPrevEntriesOpen);\n                                                    },\n                                                    title: \"Show previous entries\",\n                                                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries__trigger),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 12\n                                                    }, _this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 11\n                                                }, _this),\n                                                isPrevEntriesOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries__list),\n                                                    children: childCmp.map(function(item, idx) {\n                                                        return idx === ( false || curChildIndex) ? null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries__item),\n                                                            title: \"Back to \".concat(item.name),\n                                                            onClick: function() {\n                                                                handleBack(idx);\n                                                                setIsPrevEntriesOpen(false);\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: item.name\n                                                            }, idx, false, {\n                                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 16\n                                                            }, _this)\n                                                        }, idx, false, {\n                                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 15\n                                                        }, _this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 12\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 10\n                                        }, _this),\n                                        \"/\"\n                                    ]\n                                }, void 0, true),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    title: curChildIndex === 0 ? \"\" : \"Back to \".concat(childCmp[curChildIndex].name),\n                                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().cur__entry),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: childCmp[curChildIndex].name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 9\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 8\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 7\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__action),\n                            children: [\n                                childCmp[curChildIndex].entryIndex !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    title: \"Duplicate this entry\",\n                                    onClick: function() {\n                                        return handleDuplicate(curChildIndex);\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                        variant: \"duplicate\",\n                                        type: \"cms\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 10\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 9\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().remove__button),\n                                    title: \"Remove this entry\",\n                                    onClick: function() {\n                                        return handleRemove(curChildIndex);\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                        variant: \"remove\",\n                                        type: \"cms\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 9\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 8\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 7\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 6\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().editor__components),\n                    children: childCmp[curChildIndex].fields.map(function(param) {\n                        var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)(param, 2), key = _param[0], fValue = _param[1];\n                        var _childCmp_curChildIndex_value, _childCmp_curChildIndex;\n                        var fval = fValue;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Builder__WEBPACK_IMPORTED_MODULE_9__.FieldEditor, (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, fval), {\n                            layerPos: layerPos,\n                            name: key,\n                            size: 12,\n                            value: (_childCmp_curChildIndex = childCmp[curChildIndex]) === null || _childCmp_curChildIndex === void 0 ? void 0 : (_childCmp_curChildIndex_value = _childCmp_curChildIndex.value) === null || _childCmp_curChildIndex_value === void 0 ? void 0 : _childCmp_curChildIndex_value[key],\n                            onChange: function(props) {\n                                var _childCmp_curChildIndex;\n                                if (!((_childCmp_curChildIndex = childCmp[curChildIndex]) === null || _childCmp_curChildIndex === void 0 ? void 0 : _childCmp_curChildIndex.onChange)) return;\n                                console.log(props, key, fval, childCmp[curChildIndex].value);\n                                childCmp[curChildIndex].onChange(props);\n                            }\n                        }), key, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 9\n                        }, _this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 6\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n            lineNumber: 102,\n            columnNumber: 5\n        }, _this)\n    }, void 0, false);\n};\n_s1(LayerSidebarLayout, \"HOt8P3zlm4MIAp2I32GX9YJyhhg=\", false, function() {\n    return [\n        useClickOutside,\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__.useIsomorphicLayoutEffect\n    ];\n});\n_c = LayerSidebarLayout;\nvar _c;\n$RefreshReg$(_c, \"LayerSidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9sYXlvdXRzL2J1aWxkZXIvcGFnZS9MYXllclNpZGViYXJMYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQWtFO0FBQ3ZDO0FBQ29DO0FBQ2I7QUFDWTtBQUNWO0FBRXBELHVEQUF1RDtBQUN2RCxJQUFNVSxrQkFBa0IsU0FBQ0M7O0lBQ3hCLElBQU1DLE1BQU1QLDZDQUFNQSxDQUFpQjtJQUVuQ0MsZ0RBQVNBLENBQUM7UUFDVCxJQUFNTyxxQkFBcUIsU0FBQ0M7WUFDM0IsSUFBSUYsSUFBSUcsT0FBTyxJQUFJLENBQUNILElBQUlHLE9BQU8sQ0FBQ0MsUUFBUSxDQUFDRixNQUFNRyxNQUFNLEdBQVc7Z0JBQy9ETjtZQUNEO1FBQ0Q7UUFFQU8sU0FBU0MsZ0JBQWdCLENBQUMsYUFBYU47UUFDdkMsT0FBTztZQUNOSyxTQUFTRSxtQkFBbUIsQ0FBQyxhQUFhUDtRQUMzQztJQUNELEdBQUc7UUFBQ0Y7S0FBUztJQUViLE9BQU9DO0FBQ1I7R0FqQk1GO0FBbUJDLElBQU1XLHFCQUFxQjs7SUFDakMsSUFBTUMsVUFBVW5CLGlEQUFVQSxDQUFDSyx3RUFBa0JBO0lBQzdDLElBQVFlLFdBQWtFRCxRQUFsRUMsb0JBQThCRSx3QkFBb0NILFFBQXBDRyx1QkFBdUJDLFdBQWFKLFFBQWJJO0lBQzdELElBQTBDdEIsWUFBQUEsK0RBQUFBLENBQUFBLCtDQUFRQSxDQUFDLFFBQTVDdUIsZ0JBQW1DdkIsY0FBcEJ3QixtQkFBb0J4QjtJQUMxQyxJQUFrREEsYUFBQUEsK0RBQUFBLENBQUFBLCtDQUFRQSxDQUFDLFlBQXBEeUIsb0JBQTJDekIsZUFBeEIwQix1QkFBd0IxQjtJQUVsRCxtRUFBbUU7SUFDbkUsSUFBTTJCLGlCQUFpQnJCLGdCQUFnQjtRQUN0QyxJQUFJbUIsbUJBQW1CO1lBQ3RCQyxxQkFBcUI7UUFDdEI7SUFDRDtJQUVBLElBQU1FLGFBQWEsU0FBQ0M7UUFDbkIsSUFBTUMsU0FBVSxvRUFBR1Y7UUFDbkJXLFFBQVFDLEdBQUcsQ0FBQ0g7UUFDWixJQUFJQSxRQUFRSSxXQUFXO1lBQ3RCLElBQUlWLGtCQUFrQk0sS0FBSztZQUMzQkwsaUJBQWlCSztZQUNqQkMsT0FBT0ksTUFBTSxDQUFDTCxNQUFNLEdBQUdDLE9BQU9LLE1BQU0sR0FBSU4sQ0FBQUEsTUFBTTtRQUMvQyxPQUFPO1lBQ05MLGlCQUFpQkQsZ0JBQWdCO1lBQ2pDTyxPQUFPTSxHQUFHO1FBQ1g7UUFDQWYsc0JBQXNCUztJQUN2QjtJQUVBLElBQU1PLGVBQWUsU0FBQ1I7UUFDckJFLFFBQVFDLEdBQUcsQ0FBQyxlQUFlSDtRQUUzQixJQUFJLENBQUNULFFBQVEsQ0FBQ1MsSUFBSSxFQUFFO1FBRXBCLElBQU1TLGVBQWVsQixRQUFRLENBQUNTLElBQUk7UUFFbEMsMERBQTBEO1FBQzFELElBQUlTLGFBQWFELFlBQVksSUFBSSxPQUFPQyxhQUFhQyxVQUFVLEtBQUssVUFBVTtZQUM3RSwyREFBMkQ7WUFDM0RELGFBQWFELFlBQVksQ0FBQ0MsYUFBYUMsVUFBVTtRQUNsRDtRQUVBLG9FQUFvRTtRQUNwRSxJQUFNQyxlQUFnQixvRUFBR3BCO1FBQ3pCb0IsYUFBYU4sTUFBTSxDQUFDTCxLQUFLVyxhQUFhTCxNQUFNLEdBQUdOO1FBQy9DUixzQkFBc0JtQjtRQUV0Qix5Q0FBeUM7UUFDekMsSUFBSVgsTUFBTSxHQUFHO1lBQ1pMLGlCQUFpQkssTUFBTTtRQUN4QjtJQUNEO0lBRUEsSUFBTVksa0JBQWtCLFNBQUNaO1FBQ3hCRSxRQUFRQyxHQUFHLENBQUMsa0JBQWtCSDtRQUU5QixJQUFJLENBQUNULFFBQVEsQ0FBQ1MsSUFBSSxFQUFFO1FBRXBCLElBQU1TLGVBQWVsQixRQUFRLENBQUNTLElBQUk7UUFFbEMsNkRBQTZEO1FBQzdELElBQUlTLGFBQWFHLGVBQWUsSUFBSSxPQUFPSCxhQUFhQyxVQUFVLEtBQUssVUFBVTtZQUNoRiw4REFBOEQ7WUFDOURELGFBQWFHLGVBQWUsQ0FBQ0gsYUFBYUMsVUFBVTtRQUNyRDtJQUNBLHVEQUF1RDtJQUN4RDtJQUVBMUMsZ0lBQXlCQSxDQUFDO1FBQ3pCLElBQUksQ0FBQ3VCLFlBQVlBLFNBQVNlLE1BQU0sS0FBSyxHQUFHO1FBQ3hDWCxpQkFBaUJKLFNBQVNlLE1BQU0sR0FBRztJQUNwQyxHQUFHO1FBQUNmO0tBQVM7SUFFYixxQkFDQztrQkFDRUEsWUFBWUEsU0FBU2UsTUFBTSxHQUFHLEtBQUtmLFFBQVEsQ0FBQ0csY0FBYyxrQkFDMUQsOERBQUNtQjtZQUNBQyxXQUFXN0MsaURBQUVBLENBQ1pPLCtFQUFjLEVBQ2RBLHNGQUFxQixFQUNyQmlCLGFBQWEsS0FBS2pCLHVFQUFNLENBQUNpQixTQUFTLEdBQUc7OzhCQUd0Qyw4REFBQ29CO29CQUFJQyxXQUFXdEMsd0ZBQXVCOztzQ0FDdEMsOERBQUMwQzs0QkFBT0MsU0FBUzt1Q0FBTXBCLFdBQVdLOzs0QkFBWVUsV0FBV3RDLG9GQUFtQjtzQ0FDM0UsNEVBQUNULHVHQUFJQTtnQ0FBQ3NELE1BQUs7Z0NBQU1DLFNBQVE7Ozs7Ozs7Ozs7O3NDQUUxQiw4REFBQ0M7NEJBQUdULFdBQVU7O2dDQUNadkIsU0FBU2UsTUFBTSxHQUFHLG1CQUNsQjs7c0RBQ0MsOERBQUNPOzRDQUFJQyxXQUFXdEMsbUZBQWtCOzRDQUFFRyxLQUFLbUI7OzhEQUN4Qyw4REFBQ29CO29EQUNBQyxTQUFTOytEQUFNdEIscUJBQXFCLENBQUNEOztvREFDckM2QixPQUFNO29EQUNOWCxXQUFXdEMsNEZBQTJCOzhEQUV0Qyw0RUFBQ21EO2tFQUFLOzs7Ozs7Ozs7OztnREFFTi9CLG1DQUNBLDhEQUFDaUI7b0RBQUlDLFdBQVd0Qyx5RkFBd0I7OERBQ3RDZSxTQUFTc0MsR0FBRyxDQUFDLFNBQUNDLE1BQU05QjsrREFDcEJBLFFBQVMsT0FBQyxJQUFJTixhQUFZLElBQUsscUJBQzlCLDhEQUFDd0I7NERBRUFKLFdBQVd0Qyx5RkFBd0I7NERBQ25DaUQsT0FBTyxXQUFxQixPQUFWSyxLQUFLRSxJQUFJOzREQUMzQmIsU0FBUztnRUFDUnBCLFdBQVdDO2dFQUNYSCxxQkFBcUI7NERBQ3RCO3NFQUVBLDRFQUFDOEI7MEVBQWdCRyxLQUFLRSxJQUFJOytEQUFmaEM7Ozs7OzJEQVJOQTs7Ozs7Ozs7Ozs7Ozs7Ozs7d0NBY0w7Ozs4Q0FJUiw4REFBQ2tCO29DQUNBTyxPQUFPL0Isa0JBQWtCLElBQUksS0FBSyxXQUF3QyxPQUE3QkgsUUFBUSxDQUFDRyxjQUFjLENBQUNzQyxJQUFJO29DQUN6RWxCLFdBQVd0QyxrRkFBaUI7OENBRTVCLDRFQUFDbUQ7a0RBQU1wQyxRQUFRLENBQUNHLGNBQWMsQ0FBQ3NDLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUdyQyw4REFBQ25COzRCQUFJQyxXQUFXdEMseUZBQXdCOztnQ0FDdENlLFFBQVEsQ0FBQ0csY0FBYyxDQUFDZ0IsVUFBVSxLQUFLLG1CQUN2Qyw4REFBQ1E7b0NBQU9PLE9BQU07b0NBQXVCTixTQUFTOytDQUFNUCxnQkFBZ0JsQjs7OENBQ25FLDRFQUFDM0IsdUdBQUlBO3dDQUFDdUQsU0FBUTt3Q0FBWUQsTUFBSzs7Ozs7Ozs7Ozs7OENBR2pDLDhEQUFDSDtvQ0FDQUosV0FBV3RDLHNGQUFxQjtvQ0FDaENpRCxPQUFNO29DQUNOTixTQUFTOytDQUFNWCxhQUFhZDs7OENBRTVCLDRFQUFDM0IsdUdBQUlBO3dDQUFDdUQsU0FBUTt3Q0FBU0QsTUFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBSS9CLDhEQUFDUjtvQkFBSUMsV0FBV3RDLDBGQUF5Qjs4QkFDdkNlLFFBQVEsQ0FBQ0csY0FBYyxDQUFDMkMsTUFBTSxDQUFDUixHQUFHLENBQUM7Z0hBQUVTLGlCQUFLQzs0QkFXaENoRCwrQkFBQUE7d0JBVlYsSUFBTWlELE9BQU9EO3dCQUdiLHFCQUNDLDhEQUFDakUsNERBQVdBLEVBQUFBLG9FQUFBQSxDQUFBQSw4REFBQUEsS0FFUGtFOzRCQUNKL0MsVUFBVUE7NEJBQ1Z1QyxNQUFNTTs0QkFDTkcsTUFBTTs0QkFDTkMsS0FBSyxHQUFHbkQsMEJBQUFBLFFBQVEsQ0FBQ0csY0FBYyxjQUF2QkgsK0NBQUFBLGdDQUFBQSx3QkFBeUJtRCxLQUFLLGNBQTlCbkQsb0RBQUQsNkJBQTZELENBQUMrQyxJQUFJOzRCQUN6RUssVUFBVSxTQUFDQztvQ0FDTHJEO2dDQUFMLElBQUksR0FBQ0EsMEJBQUFBLFFBQVEsQ0FBQ0csY0FBYyxjQUF2QkgsOENBQUFBLHdCQUF5Qm9ELFFBQVEsR0FBRTtnQ0FDeEN6QyxRQUFRQyxHQUFHLENBQUN5QyxPQUFPTixLQUFLRSxNQUFNakQsUUFBUSxDQUFDRyxjQUFjLENBQUNnRCxLQUFLO2dDQUMzRG5ELFFBQVEsQ0FBQ0csY0FBYyxDQUFDaUQsUUFBUSxDQUFDQzs0QkFDbEM7NEJBVktOOzs7OztvQkFhUjs7Ozs7Ozs7Ozs7OztBQU1OLEVBQUM7SUF2S1lsRDs7UUFPV1g7UUEyRHZCVCw0SEFBeUJBOzs7S0FsRWJvQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvbGF5b3V0cy9idWlsZGVyL3BhZ2UvTGF5ZXJTaWRlYmFyTGF5b3V0LnRzeD9kNjZkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEljb24sIHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QgfSBmcm9tICdAY29sbGVjdGl2ZS9jb3JlJ1xuaW1wb3J0IGNuIGZyb20gJ2NsYXNzbmFtZXMnXG5pbXBvcnQgeyB1c2VDb250ZXh0LCB1c2VTdGF0ZSwgdXNlUmVmLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IEZpZWxkRWRpdG9yIH0gZnJvbSAnQC9jb21wb25lbnRzL0J1aWxkZXInXG5pbXBvcnQgeyBQYWdlQnVpbGRlckNvbnRleHQgfSBmcm9tICdAL2NvbnRleHRzL0J1aWxkZXJDb250ZXh0J1xuaW1wb3J0IHN0eWxlcyBmcm9tICcuL3BhZ2VidWlsZGVybGF5b3V0Lm1vZHVsZS5zY3NzJ1xuXG4vLyBDdXN0b20gaG9vayDEkeG7gyBwaMOhdCBoaeG7h24gY2xpY2sgYsOqbiBuZ2/DoGkgbeG7mXQgcGjhuqduIHThu61cbmNvbnN0IHVzZUNsaWNrT3V0c2lkZSA9IChjYWxsYmFjazogKCkgPT4gdm9pZCkgPT4ge1xuXHRjb25zdCByZWYgPSB1c2VSZWY8SFRNTERpdkVsZW1lbnQ+KG51bGwpXG5cblx0dXNlRWZmZWN0KCgpID0+IHtcblx0XHRjb25zdCBoYW5kbGVDbGlja091dHNpZGUgPSAoZXZlbnQ6IE1vdXNlRXZlbnQpID0+IHtcblx0XHRcdGlmIChyZWYuY3VycmVudCAmJiAhcmVmLmN1cnJlbnQuY29udGFpbnMoZXZlbnQudGFyZ2V0IGFzIE5vZGUpKSB7XG5cdFx0XHRcdGNhbGxiYWNrKClcblx0XHRcdH1cblx0XHR9XG5cblx0XHRkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCdtb3VzZWRvd24nLCBoYW5kbGVDbGlja091dHNpZGUpXG5cdFx0cmV0dXJuICgpID0+IHtcblx0XHRcdGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21vdXNlZG93bicsIGhhbmRsZUNsaWNrT3V0c2lkZSlcblx0XHR9XG5cdH0sIFtjYWxsYmFja10pXG5cblx0cmV0dXJuIHJlZlxufVxuXG5leHBvcnQgY29uc3QgTGF5ZXJTaWRlYmFyTGF5b3V0ID0gKCkgPT4ge1xuXHRjb25zdCBjb250ZXh0ID0gdXNlQ29udGV4dChQYWdlQnVpbGRlckNvbnRleHQpXG5cdGNvbnN0IHsgY2hpbGRDb21wb25lbnREYXRhOiBjaGlsZENtcCwgc2V0Q2hpbGRDb21wb25lbnREYXRhLCBsYXllclBvcyB9ID0gY29udGV4dFxuXHRjb25zdCBbY3VyQ2hpbGRJbmRleCwgc2V0Q3VyQ2hpbGRJbmRleF0gPSB1c2VTdGF0ZSgwKVxuXHRjb25zdCBbaXNQcmV2RW50cmllc09wZW4sIHNldElzUHJldkVudHJpZXNPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKVxuXG5cdC8vIFPhu60gZOG7pW5nIGhvb2sgdXNlQ2xpY2tPdXRzaWRlIMSR4buDIMSRw7NuZyBkcm9wZG93biBraGkgY2xpY2sgcmEgbmdvw6BpXG5cdGNvbnN0IHByZXZFbnRyaWVzUmVmID0gdXNlQ2xpY2tPdXRzaWRlKCgpID0+IHtcblx0XHRpZiAoaXNQcmV2RW50cmllc09wZW4pIHtcblx0XHRcdHNldElzUHJldkVudHJpZXNPcGVuKGZhbHNlKVxuXHRcdH1cblx0fSlcblxuXHRjb25zdCBoYW5kbGVCYWNrID0gKGlkeDogbnVtYmVyIHwgdW5kZWZpbmVkKSA9PiB7XG5cdFx0Y29uc3QgbmV3VmFsID0gWy4uLmNoaWxkQ21wXVxuXHRcdGNvbnNvbGUubG9nKGlkeClcblx0XHRpZiAoaWR4ICE9PSB1bmRlZmluZWQpIHtcblx0XHRcdGlmIChjdXJDaGlsZEluZGV4ID09PSBpZHgpIHJldHVyblxuXHRcdFx0c2V0Q3VyQ2hpbGRJbmRleChpZHgpXG5cdFx0XHRuZXdWYWwuc3BsaWNlKGlkeCArIDEsIG5ld1ZhbC5sZW5ndGggLSAoaWR4ICsgMSkpXG5cdFx0fSBlbHNlIHtcblx0XHRcdHNldEN1ckNoaWxkSW5kZXgoY3VyQ2hpbGRJbmRleCAtIDEpXG5cdFx0XHRuZXdWYWwucG9wKClcblx0XHR9XG5cdFx0c2V0Q2hpbGRDb21wb25lbnREYXRhKG5ld1ZhbClcblx0fVxuXG5cdGNvbnN0IGhhbmRsZVJlbW92ZSA9IChpZHg6IG51bWJlcikgPT4ge1xuXHRcdGNvbnNvbGUubG9nKCdSZW1vdmUgaWR4OicsIGlkeClcblxuXHRcdGlmICghY2hpbGRDbXBbaWR4XSkgcmV0dXJuXG5cblx0XHRjb25zdCBjdXJyZW50RW50cnkgPSBjaGlsZENtcFtpZHhdXG5cblx0XHQvLyBVc2UgdGhlIGhhbmRsZVJlbW92ZSBmdW5jdGlvbiBwYXNzZWQgZnJvbSB0aGUgQ29tcG9uZW50XG5cdFx0aWYgKGN1cnJlbnRFbnRyeS5oYW5kbGVSZW1vdmUgJiYgdHlwZW9mIGN1cnJlbnRFbnRyeS5lbnRyeUluZGV4ID09PSAnbnVtYmVyJykge1xuXHRcdFx0Ly8gQ2FsbCB0aGUgQ29tcG9uZW50J3MgaGFuZGxlUmVtb3ZlIHdpdGggdGhlIGNvcnJlY3QgaW5kZXhcblx0XHRcdGN1cnJlbnRFbnRyeS5oYW5kbGVSZW1vdmUoY3VycmVudEVudHJ5LmVudHJ5SW5kZXgpXG5cdFx0fVxuXG5cdFx0Ly8gVXBkYXRlIGNoaWxkQ29tcG9uZW50RGF0YSAtIHJlbW92ZSBjdXJyZW50IGFuZCBzdWJzZXF1ZW50IGVudHJpZXNcblx0XHRjb25zdCBuZXdDaGlsZERhdGEgPSBbLi4uY2hpbGRDbXBdXG5cdFx0bmV3Q2hpbGREYXRhLnNwbGljZShpZHgsIG5ld0NoaWxkRGF0YS5sZW5ndGggLSBpZHgpXG5cdFx0c2V0Q2hpbGRDb21wb25lbnREYXRhKG5ld0NoaWxkRGF0YSlcblxuXHRcdC8vIFVwZGF0ZSBjdXJyZW50IGluZGV4IHRvIHByZXZpb3VzIGxldmVsXG5cdFx0aWYgKGlkeCA+IDApIHtcblx0XHRcdHNldEN1ckNoaWxkSW5kZXgoaWR4IC0gMSlcblx0XHR9XG5cdH1cblxuXHRjb25zdCBoYW5kbGVEdXBsaWNhdGUgPSAoaWR4OiBudW1iZXIpID0+IHtcblx0XHRjb25zb2xlLmxvZygnRHVwbGljYXRlIGlkeDonLCBpZHgpXG5cblx0XHRpZiAoIWNoaWxkQ21wW2lkeF0pIHJldHVyblxuXG5cdFx0Y29uc3QgY3VycmVudEVudHJ5ID0gY2hpbGRDbXBbaWR4XVxuXG5cdFx0Ly8gVXNlIHRoZSBoYW5kbGVEdXBsaWNhdGUgZnVuY3Rpb24gcGFzc2VkIGZyb20gdGhlIENvbXBvbmVudFxuXHRcdGlmIChjdXJyZW50RW50cnkuaGFuZGxlRHVwbGljYXRlICYmIHR5cGVvZiBjdXJyZW50RW50cnkuZW50cnlJbmRleCA9PT0gJ251bWJlcicpIHtcblx0XHRcdC8vIENhbGwgdGhlIENvbXBvbmVudCdzIGhhbmRsZUR1cGxpY2F0ZSB3aXRoIHRoZSBjb3JyZWN0IGluZGV4XG5cdFx0XHRjdXJyZW50RW50cnkuaGFuZGxlRHVwbGljYXRlKGN1cnJlbnRFbnRyeS5lbnRyeUluZGV4KVxuXHRcdH1cblx0XHQvLyBOb3RlOiBOb24tcmVwZWF0YWJsZSBjb21wb25lbnRzIGNhbm5vdCBiZSBkdXBsaWNhdGVkXG5cdH1cblxuXHR1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0KCgpID0+IHtcblx0XHRpZiAoIWNoaWxkQ21wIHx8IGNoaWxkQ21wLmxlbmd0aCA9PT0gMCkgcmV0dXJuXG5cdFx0c2V0Q3VyQ2hpbGRJbmRleChjaGlsZENtcC5sZW5ndGggLSAxKVxuXHR9LCBbY2hpbGRDbXBdKVxuXG5cdHJldHVybiAoXG5cdFx0PD5cblx0XHRcdHtjaGlsZENtcCAmJiBjaGlsZENtcC5sZW5ndGggPiAwICYmIGNoaWxkQ21wW2N1ckNoaWxkSW5kZXhdICYmIChcblx0XHRcdFx0PGRpdlxuXHRcdFx0XHRcdGNsYXNzTmFtZT17Y24oXG5cdFx0XHRcdFx0XHRzdHlsZXMuc2lkZWJhcixcblx0XHRcdFx0XHRcdHN0eWxlcy5zaWRlYmFyX19sYXllcixcblx0XHRcdFx0XHRcdGxheWVyUG9zICE9PSAnJyA/IHN0eWxlc1tsYXllclBvc10gOiAnJ1xuXHRcdFx0XHRcdCl9XG5cdFx0XHRcdD5cblx0XHRcdFx0XHQ8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLmNvbXBvbmVudF9fdGl0bGV9PlxuXHRcdFx0XHRcdFx0PGJ1dHRvbiBvbkNsaWNrPXsoKSA9PiBoYW5kbGVCYWNrKHVuZGVmaW5lZCl9IGNsYXNzTmFtZT17c3R5bGVzLmJhY2tfX2J1dHRvbn0+XG5cdFx0XHRcdFx0XHRcdDxJY29uIHR5cGU9XCJjbXNcIiB2YXJpYW50PVwiYmFja1wiIC8+XG5cdFx0XHRcdFx0XHQ8L2J1dHRvbj5cblx0XHRcdFx0XHRcdDxoNiBjbGFzc05hbWU9XCJjb2xsZWN0X19oZWFkaW5nIGNvbGxlY3RfX2hlYWRpbmctLWg2XCI+XG5cdFx0XHRcdFx0XHRcdHtjaGlsZENtcC5sZW5ndGggPiAxICYmIChcblx0XHRcdFx0XHRcdFx0XHQ8PlxuXHRcdFx0XHRcdFx0XHRcdFx0PGRpdiBjbGFzc05hbWU9e3N0eWxlcy5wcmV2ZW50cmllc30gcmVmPXtwcmV2RW50cmllc1JlZn0+XG5cdFx0XHRcdFx0XHRcdFx0XHRcdDxidXR0b25cblx0XHRcdFx0XHRcdFx0XHRcdFx0XHRvbkNsaWNrPXsoKSA9PiBzZXRJc1ByZXZFbnRyaWVzT3BlbighaXNQcmV2RW50cmllc09wZW4pfVxuXHRcdFx0XHRcdFx0XHRcdFx0XHRcdHRpdGxlPVwiU2hvdyBwcmV2aW91cyBlbnRyaWVzXCJcblx0XHRcdFx0XHRcdFx0XHRcdFx0XHRjbGFzc05hbWU9e3N0eWxlcy5wcmV2ZW50cmllc19fdHJpZ2dlcn1cblx0XHRcdFx0XHRcdFx0XHRcdFx0PlxuXHRcdFx0XHRcdFx0XHRcdFx0XHRcdDxzcGFuPi4uLjwvc3Bhbj5cblx0XHRcdFx0XHRcdFx0XHRcdFx0PC9idXR0b24+XG5cdFx0XHRcdFx0XHRcdFx0XHRcdHtpc1ByZXZFbnRyaWVzT3BlbiAmJiAoXG5cdFx0XHRcdFx0XHRcdFx0XHRcdFx0PGRpdiBjbGFzc05hbWU9e3N0eWxlcy5wcmV2ZW50cmllc19fbGlzdH0+XG5cdFx0XHRcdFx0XHRcdFx0XHRcdFx0XHR7Y2hpbGRDbXAubWFwKChpdGVtLCBpZHgpID0+XG5cdFx0XHRcdFx0XHRcdFx0XHRcdFx0XHRcdGlkeCA9PT0gKDAgfHwgY3VyQ2hpbGRJbmRleCkgPyBudWxsIDogKFxuXHRcdFx0XHRcdFx0XHRcdFx0XHRcdFx0XHRcdDxidXR0b25cblx0XHRcdFx0XHRcdFx0XHRcdFx0XHRcdFx0XHRcdGtleT17aWR4fVxuXHRcdFx0XHRcdFx0XHRcdFx0XHRcdFx0XHRcdFx0Y2xhc3NOYW1lPXtzdHlsZXMucHJldmVudHJpZXNfX2l0ZW19XG5cdFx0XHRcdFx0XHRcdFx0XHRcdFx0XHRcdFx0XHR0aXRsZT17YEJhY2sgdG8gJHtpdGVtLm5hbWV9YH1cblx0XHRcdFx0XHRcdFx0XHRcdFx0XHRcdFx0XHRcdG9uQ2xpY2s9eygpID0+IHtcblx0XHRcdFx0XHRcdFx0XHRcdFx0XHRcdFx0XHRcdFx0aGFuZGxlQmFjayhpZHgpXG5cdFx0XHRcdFx0XHRcdFx0XHRcdFx0XHRcdFx0XHRcdHNldElzUHJldkVudHJpZXNPcGVuKGZhbHNlKVxuXHRcdFx0XHRcdFx0XHRcdFx0XHRcdFx0XHRcdFx0fX1cblx0XHRcdFx0XHRcdFx0XHRcdFx0XHRcdFx0XHQ+XG5cdFx0XHRcdFx0XHRcdFx0XHRcdFx0XHRcdFx0XHQ8c3BhbiBrZXk9e2lkeH0+e2l0ZW0ubmFtZX08L3NwYW4+XG5cdFx0XHRcdFx0XHRcdFx0XHRcdFx0XHRcdFx0PC9idXR0b24+XG5cdFx0XHRcdFx0XHRcdFx0XHRcdFx0XHRcdClcblx0XHRcdFx0XHRcdFx0XHRcdFx0XHRcdCl9XG5cdFx0XHRcdFx0XHRcdFx0XHRcdFx0PC9kaXY+XG5cdFx0XHRcdFx0XHRcdFx0XHRcdCl9XG5cdFx0XHRcdFx0XHRcdFx0XHQ8L2Rpdj5cblx0XHRcdFx0XHRcdFx0XHRcdC9cblx0XHRcdFx0XHRcdFx0XHQ8Lz5cblx0XHRcdFx0XHRcdFx0KX1cblx0XHRcdFx0XHRcdFx0PGJ1dHRvblxuXHRcdFx0XHRcdFx0XHRcdHRpdGxlPXtjdXJDaGlsZEluZGV4ID09PSAwID8gJycgOiBgQmFjayB0byAke2NoaWxkQ21wW2N1ckNoaWxkSW5kZXhdLm5hbWV9YH1cblx0XHRcdFx0XHRcdFx0XHRjbGFzc05hbWU9e3N0eWxlcy5jdXJfX2VudHJ5fVxuXHRcdFx0XHRcdFx0XHQ+XG5cdFx0XHRcdFx0XHRcdFx0PHNwYW4+e2NoaWxkQ21wW2N1ckNoaWxkSW5kZXhdLm5hbWV9PC9zcGFuPlxuXHRcdFx0XHRcdFx0XHQ8L2J1dHRvbj5cblx0XHRcdFx0XHRcdDwvaDY+XG5cdFx0XHRcdFx0XHQ8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLmNvbXBvbmVudF9fYWN0aW9ufT5cblx0XHRcdFx0XHRcdFx0e2NoaWxkQ21wW2N1ckNoaWxkSW5kZXhdLmVudHJ5SW5kZXggIT09IDAgJiYgKFxuXHRcdFx0XHRcdFx0XHRcdDxidXR0b24gdGl0bGU9XCJEdXBsaWNhdGUgdGhpcyBlbnRyeVwiIG9uQ2xpY2s9eygpID0+IGhhbmRsZUR1cGxpY2F0ZShjdXJDaGlsZEluZGV4KX0+XG5cdFx0XHRcdFx0XHRcdFx0XHQ8SWNvbiB2YXJpYW50PVwiZHVwbGljYXRlXCIgdHlwZT1cImNtc1wiIC8+XG5cdFx0XHRcdFx0XHRcdFx0PC9idXR0b24+XG5cdFx0XHRcdFx0XHRcdCl9XG5cdFx0XHRcdFx0XHRcdDxidXR0b25cblx0XHRcdFx0XHRcdFx0XHRjbGFzc05hbWU9e3N0eWxlcy5yZW1vdmVfX2J1dHRvbn1cblx0XHRcdFx0XHRcdFx0XHR0aXRsZT1cIlJlbW92ZSB0aGlzIGVudHJ5XCJcblx0XHRcdFx0XHRcdFx0XHRvbkNsaWNrPXsoKSA9PiBoYW5kbGVSZW1vdmUoY3VyQ2hpbGRJbmRleCl9XG5cdFx0XHRcdFx0XHRcdD5cblx0XHRcdFx0XHRcdFx0XHQ8SWNvbiB2YXJpYW50PVwicmVtb3ZlXCIgdHlwZT1cImNtc1wiIC8+XG5cdFx0XHRcdFx0XHRcdDwvYnV0dG9uPlxuXHRcdFx0XHRcdFx0PC9kaXY+XG5cdFx0XHRcdFx0PC9kaXY+XG5cdFx0XHRcdFx0PGRpdiBjbGFzc05hbWU9e3N0eWxlcy5lZGl0b3JfX2NvbXBvbmVudHN9PlxuXHRcdFx0XHRcdFx0e2NoaWxkQ21wW2N1ckNoaWxkSW5kZXhdLmZpZWxkcy5tYXAoKFtrZXksIGZWYWx1ZV0pID0+IHtcblx0XHRcdFx0XHRcdFx0Y29uc3QgZnZhbCA9IGZWYWx1ZSBhcyB7XG5cdFx0XHRcdFx0XHRcdFx0dHlwZTogc3RyaW5nXG5cdFx0XHRcdFx0XHRcdH1cblx0XHRcdFx0XHRcdFx0cmV0dXJuIChcblx0XHRcdFx0XHRcdFx0XHQ8RmllbGRFZGl0b3Jcblx0XHRcdFx0XHRcdFx0XHRcdGtleT17a2V5fVxuXHRcdFx0XHRcdFx0XHRcdFx0ey4uLmZ2YWx9XG5cdFx0XHRcdFx0XHRcdFx0XHRsYXllclBvcz17bGF5ZXJQb3N9XG5cdFx0XHRcdFx0XHRcdFx0XHRuYW1lPXtrZXl9XG5cdFx0XHRcdFx0XHRcdFx0XHRzaXplPXsxMn1cblx0XHRcdFx0XHRcdFx0XHRcdHZhbHVlPXsoY2hpbGRDbXBbY3VyQ2hpbGRJbmRleF0/LnZhbHVlIGFzIFJlY29yZDxzdHJpbmcsIHVua25vd24+KT8uW2tleV19XG5cdFx0XHRcdFx0XHRcdFx0XHRvbkNoYW5nZT17KHByb3BzKSA9PiB7XG5cdFx0XHRcdFx0XHRcdFx0XHRcdGlmICghY2hpbGRDbXBbY3VyQ2hpbGRJbmRleF0/Lm9uQ2hhbmdlKSByZXR1cm5cblx0XHRcdFx0XHRcdFx0XHRcdFx0Y29uc29sZS5sb2cocHJvcHMsIGtleSwgZnZhbCwgY2hpbGRDbXBbY3VyQ2hpbGRJbmRleF0udmFsdWUpXG5cdFx0XHRcdFx0XHRcdFx0XHRcdGNoaWxkQ21wW2N1ckNoaWxkSW5kZXhdLm9uQ2hhbmdlKHByb3BzKVxuXHRcdFx0XHRcdFx0XHRcdFx0fX1cblx0XHRcdFx0XHRcdFx0XHQvPlxuXHRcdFx0XHRcdFx0XHQpXG5cdFx0XHRcdFx0XHR9KX1cblx0XHRcdFx0XHQ8L2Rpdj5cblx0XHRcdFx0PC9kaXY+XG5cdFx0XHQpfVxuXHRcdDwvPlxuXHQpXG59XG4iXSwibmFtZXMiOlsiSWNvbiIsInVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QiLCJjbiIsInVzZUNvbnRleHQiLCJ1c2VTdGF0ZSIsInVzZVJlZiIsInVzZUVmZmVjdCIsIkZpZWxkRWRpdG9yIiwiUGFnZUJ1aWxkZXJDb250ZXh0Iiwic3R5bGVzIiwidXNlQ2xpY2tPdXRzaWRlIiwiY2FsbGJhY2siLCJyZWYiLCJoYW5kbGVDbGlja091dHNpZGUiLCJldmVudCIsImN1cnJlbnQiLCJjb250YWlucyIsInRhcmdldCIsImRvY3VtZW50IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJMYXllclNpZGViYXJMYXlvdXQiLCJjb250ZXh0IiwiY2hpbGRDb21wb25lbnREYXRhIiwiY2hpbGRDbXAiLCJzZXRDaGlsZENvbXBvbmVudERhdGEiLCJsYXllclBvcyIsImN1ckNoaWxkSW5kZXgiLCJzZXRDdXJDaGlsZEluZGV4IiwiaXNQcmV2RW50cmllc09wZW4iLCJzZXRJc1ByZXZFbnRyaWVzT3BlbiIsInByZXZFbnRyaWVzUmVmIiwiaGFuZGxlQmFjayIsImlkeCIsIm5ld1ZhbCIsImNvbnNvbGUiLCJsb2ciLCJ1bmRlZmluZWQiLCJzcGxpY2UiLCJsZW5ndGgiLCJwb3AiLCJoYW5kbGVSZW1vdmUiLCJjdXJyZW50RW50cnkiLCJlbnRyeUluZGV4IiwibmV3Q2hpbGREYXRhIiwiaGFuZGxlRHVwbGljYXRlIiwiZGl2IiwiY2xhc3NOYW1lIiwic2lkZWJhciIsInNpZGViYXJfX2xheWVyIiwiY29tcG9uZW50X190aXRsZSIsImJ1dHRvbiIsIm9uQ2xpY2siLCJiYWNrX19idXR0b24iLCJ0eXBlIiwidmFyaWFudCIsImg2IiwicHJldmVudHJpZXMiLCJ0aXRsZSIsInByZXZlbnRyaWVzX190cmlnZ2VyIiwic3BhbiIsInByZXZlbnRyaWVzX19saXN0IiwibWFwIiwiaXRlbSIsInByZXZlbnRyaWVzX19pdGVtIiwibmFtZSIsImN1cl9fZW50cnkiLCJjb21wb25lbnRfX2FjdGlvbiIsInJlbW92ZV9fYnV0dG9uIiwiZWRpdG9yX19jb21wb25lbnRzIiwiZmllbGRzIiwia2V5IiwiZlZhbHVlIiwiZnZhbCIsInNpemUiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwicHJvcHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/layouts/builder/page/LayerSidebarLayout.tsx\n"));

/***/ })

});