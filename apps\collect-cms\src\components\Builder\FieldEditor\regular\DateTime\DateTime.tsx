import { Button, Icon, Input } from '@collective/core'
import cn from 'classnames'
import { usePathname } from 'next/navigation'
import { useEffect, useMemo, useRef, useState } from 'react'
import type { FieldProps } from '../../FieldEditor'

export interface DateTimeProps<T> extends FieldProps<T> {
	value?: T
	onChange: (props: { field: string; value: string }) => void
}

type DatetimeType = {
	date?: string
	time?: string
	timezone?: string
}

export const DateTime = <T,>(props: DateTimeProps<T>) => {
	const pathname = usePathname()
	const { type, required, value, onChange, name } = props
	const propsType = type ?? ''
	const [propsValue, setPropsValue] = useState(value ?? '')
	const [datetime, setDatetime] = useState<DatetimeType>()
	const dateRef = useRef<HTMLInputElement>(null)
	const timeRef = useRef<HTMLInputElement>(null)
	const otherRef = useRef<HTMLInputElement>(null)

	const handleIconClick = (ref: React.RefObject<HTMLInputElement>) => {
		if (ref?.current?.showPicker) {
			ref.current.showPicker()
		} else {
			ref?.current?.focus() // fallback
		}
	}

	const handleDatetimeConvert = (isoString: string) => {
		const dateObj = new Date(isoString)
		const date = dateObj.toISOString().split('T')[0] // '2025-04-22'
		const time = dateObj.toISOString().split('T')[1]?.slice(0, 5) // '05:00'
		const timezone = 'UTC' // 'Z'

		setDatetime({
			date: date,
			time: time,
			timezone: timezone,
		})
	}

	const handleChangeDatetime = (field: keyof DatetimeType, value: string) => {
		const newDatetime = { ...datetime, [field]: value }
		setDatetime(newDatetime)

		const formatted = handleDatetimeRevert(newDatetime)
		if (formatted) {
			onChange?.({ field: name as string, value: formatted })
		}
	}

	const handleDatetimeRevert = (dt: DatetimeType): string | undefined => {
		if (!dt.date || !dt.time) return undefined
		const tz = dt.timezone ?? 'Z'
		return `${dt.date}T${dt.time}:00${tz}`
	}

	useEffect(() => {
		if (propsType === 'datetime') {
			handleDatetimeConvert(propsValue as string)
		}
	}, [propsType, propsValue])

	const isBuilderMode = useMemo(() => pathname?.startsWith('/content-builder/'), [pathname])

	return (
		<>
			{propsType === 'datetime' && datetime ? (
				<div className={cn('collect__input-group', isBuilderMode ? 'stacked' : '')}>
					<Input
						ref={dateRef}
						type="date"
						className="collect__input has__border clickable"
						required={required}
						value={datetime.date}
						onChange={(e) => handleChangeDatetime('date', e.target.value)}
						onClick={() => handleIconClick(dateRef)}
						endIcon={
							<button onClick={() => handleIconClick(dateRef)}>
								<Icon type="cms" variant="date" />
							</button>
						}
					/>
					<Input
						ref={timeRef}
						type="time"
						className="collect__input has__border clickable"
						required={required}
						value={datetime.time}
						onChange={(e) => handleChangeDatetime('time', e.target.value)}
						onClick={() => handleIconClick(timeRef)}
						endIcon={<button onClick={() => handleIconClick(timeRef)}><Icon type="cms" variant="time"} /></button>}
					/>
				</div>
			) : (
				<Input
					ref={otherRef}
					type={propsType}
					className="collect__input has__border clickable"
					required={required}
					value={propsValue as string}
					onChange={(e) => {
						setPropsValue(e.target.value)
						onChange?.({ field: name as string, value: e.target.value })
					}}
					onClick={() => handleIconClick(otherRef)}
					endIcon={
						<Icon type="cms" variant={propsType} onClick={() => handleIconClick(otherRef)} />
					}
				/>
			)}
		</>
	)
}
