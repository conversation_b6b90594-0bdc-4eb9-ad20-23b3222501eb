"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(pagebuilder)/content-builder/[...slug]/layout",{

/***/ "(app-pages-browser)/./src/components/Builder/LayoutEditor/LayoutEditor.tsx":
/*!**************************************************************!*\
  !*** ./src/components/Builder/LayoutEditor/LayoutEditor.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LayoutEditor: function() { return /* binding */ LayoutEditor; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/_/_define_property */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_define_property.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect,useWindowDimensions!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useWindowDimensions.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect,useWindowDimensions!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect,useWindowDimensions!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var _collective_ui_lib_src_base_Wrapper__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @collective/ui-lib/src/base/Wrapper */ \"(app-pages-browser)/../../packages/ui-lib/src/base/Wrapper.tsx\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _ComponentMenu__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../ComponentMenu */ \"(app-pages-browser)/./src/components/Builder/ComponentMenu/ComponentMenu.tsx\");\n/* harmony import */ var _ComponentQuickActions__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../ComponentQuickActions */ \"(app-pages-browser)/./src/components/Builder/ComponentQuickActions/ComponentQuickActions.tsx\");\n/* harmony import */ var _Dnd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../Dnd */ \"(app-pages-browser)/./src/components/Builder/Dnd/Board.tsx\");\n/* harmony import */ var _layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./layouteditor.module.scss */ \"(app-pages-browser)/./src/components/Builder/LayoutEditor/layouteditor.module.scss\");\n/* harmony import */ var _layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nvar LayoutEditor = function(param) {\n    var children = param.children;\n    _s();\n    var _s1 = $RefreshSig$();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__.PageBuilderContext);\n    var data = context.data, contentType = context.contentType, configuration = context.configuration, setData = context.setData, components = context.components, setEditingIden = context.setEditingIden, normalizedData = context.normalizedData, setChildComponentData = context.setChildComponentData;\n    var _ref = data !== null && data !== void 0 ? data : {}, commonData = _ref.data;\n    var _ref1 = contentType !== null && contentType !== void 0 ? contentType : {}, uidConfig = _ref1.data;\n    var _ref2 = components !== null && components !== void 0 ? components : {}, uiConfig = _ref2.data;\n    var windowDimension = (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_5__.useWindowDimensions)();\n    // Handle Headline change\n    var globalField = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function() {\n        if (!contentType.data || !configuration.data) return \"\";\n        var settings = configuration.data.contentType.settings;\n        var mainFieldKey = settings.mainField;\n        return mainFieldKey;\n    }, [\n        contentType,\n        configuration\n    ]);\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(commonData && globalField ? commonData[globalField] : \"\"), 2), headline = _useState[0], setHeadline = _useState[1];\n    var textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    var handleChange = function(event) {\n        if (!textareaRef.current) return;\n        var target = event.target;\n        setHeadline(target.value);\n        setData((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, data), {\n            data: (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, data.data), (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_9__._)({}, globalField, target.value))\n        }));\n        textareaRef.current.style.height = \"auto\";\n        textareaRef.current.style.height = textareaRef.current.scrollHeight + \"px\";\n    };\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__.useIsomorphicLayoutEffect)(function() {\n        if (!textareaRef.current) return;\n        textareaRef.current.style.height = \"auto\";\n        textareaRef.current.style.height = textareaRef.current.scrollHeight + \"px\";\n    }, [\n        textareaRef,\n        windowDimension\n    ]);\n    // Handle component menu\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null), 2), menu = _useState1[0], setMenu = _useState1[1];\n    var triggerMenu = function(e) {\n        var container = e.currentTarget;\n        setMenu(function(prev) {\n            return prev !== container ? container : null;\n        });\n    };\n    var handleAddBlock = function(component) {\n        if (!component) return setMenu(null);\n        var id = Number(menu === null || menu === void 0 ? void 0 : menu.dataset.id);\n        var defaultData = uiConfig.find(function(item) {\n            return item.uid === component.uid;\n        });\n        var attributes = defaultData === null || defaultData === void 0 ? void 0 : defaultData.schema.attributes;\n        if (!attributes) return setMenu(null);\n        var remapProps = Object.entries(attributes).reduce(function(acc, param) {\n            var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)(param, 2), key = _param[0], value = _param[1];\n            var newValue;\n            switch(value.type){\n                case \"boolean\":\n                    var _value_default;\n                    newValue = (_value_default = value[\"default\"]) !== null && _value_default !== void 0 ? _value_default : false;\n                    break;\n                case \"string\":\n                    newValue = \"\";\n                    break;\n                default:\n                    newValue = null;\n                    break;\n            }\n            acc[key] = newValue;\n            return acc;\n        }, {});\n        var addData = (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({\n            __component: component.uid\n        }, remapProps), {\n            __temp_key__: normalizedData.components.length + 1\n        });\n        var components = normalizedData.components;\n        var index = components.findIndex(function(component) {\n            return component.__temp_key__ === id;\n        });\n        components.splice(index + 1, 0, addData);\n        setData((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, data), {\n            data: (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, data.data), {\n                components: components\n            })\n        }));\n        setMenu(null);\n    };\n    // Get list available components\n    var Modules = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function() {\n        if (!uidConfig) return {};\n        if (!uidConfig.schema.attributes.components) return {};\n        if (\"components\" in uidConfig.schema.attributes.components === false) return {};\n        var components = {};\n        var arrComponents = uidConfig.schema.attributes.components.components;\n        arrComponents === null || arrComponents === void 0 ? void 0 : arrComponents.forEach(function(module) {\n            var Component = (0,_collective_ui_lib_src_base_Wrapper__WEBPACK_IMPORTED_MODULE_11__.CmsWrapper)({\n                module: module\n            });\n            if (Component && components) components[module] = Component;\n        });\n        return components;\n    }, [\n        uidConfig\n    ]);\n    // Component wrapper with hover state\n    var ComponentWrapper = function(param) {\n        var column = param.column, index = param.index;\n        _s1();\n        var Module = (column === null || column === void 0 ? void 0 : column.__component) && Modules && Modules[column.__component];\n        var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), 2), isHovered = _useState[0], setIsHovered = _useState[1];\n        var handleEdit = function() {\n            if (!column) return;\n            var id = column.id || column.__temp_key__;\n            setEditingIden({\n                key: column.__component,\n                id: id\n            });\n        // setChildComponentData({ name: '', value: {}, fields: [['', {}] as Entry] })\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            tabIndex: 0,\n            role: \"button\",\n            className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__block),\n            onClick: handleEdit,\n            onKeyDown: handleEdit,\n            onMouseEnter: function() {\n                return setIsHovered(true);\n            },\n            onMouseLeave: function() {\n                return setIsHovered(false);\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ComponentQuickActions__WEBPACK_IMPORTED_MODULE_12__.ComponentQuickActions, {\n                    index: index,\n                    id: column === null || column === void 0 ? void 0 : column.__temp_key__,\n                    isVisible: isHovered\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 5\n                }, _this),\n                Module ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Module, (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, column), void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 6\n                }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        \"Component \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                            children: column === null || column === void 0 ? void 0 : column.__component\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 17\n                        }, _this),\n                        \" failed to import/load\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 6\n                }, _this)\n            ]\n        }, column === null || column === void 0 ? void 0 : column.__temp_key__, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n            lineNumber: 158,\n            columnNumber: 4\n        }, _this);\n    };\n    _s1(ComponentWrapper, \"FPQn8a98tPjpohC7NUYORQR8GJE=\");\n    // Column component for Board\n    var ColumnComponent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(props) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ComponentWrapper, (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, props), void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n            lineNumber: 187,\n            columnNumber: 11\n        }, _this);\n    }, // ComponentWrapper is defined in the component scope, so it doesn't need to be in the dependency array\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        Modules,\n        setEditingIden\n    ]);\n    var ColumnAddBlock = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(param) {\n        var column = param.column;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            \"data-id\": column === null || column === void 0 ? void 0 : column.__temp_key__,\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"text__w--icon align__center add__block\", (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().add__block)),\n            onClick: triggerMenu,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_13__.Icon, {\n                    variant: \"plus-circle\",\n                    type: \"cms\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 5\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().line)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 5\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n            lineNumber: 196,\n            columnNumber: 4\n        }, _this);\n    }, []);\n    // Toggle active when trigger menu\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__.useIsomorphicLayoutEffect)(function() {\n        var allBlockBtn = document.querySelectorAll(\".add__block\");\n        var _iteratorNormalCompletion = true, _didIteratorError = false, _iteratorError = undefined;\n        try {\n            for(var _iterator = allBlockBtn[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true){\n                var button = _step.value;\n                button.classList.toggle(\"active\", menu === button);\n            }\n        } catch (err) {\n            _didIteratorError = true;\n            _iteratorError = err;\n        } finally{\n            try {\n                if (!_iteratorNormalCompletion && _iterator[\"return\"] != null) {\n                    _iterator[\"return\"]();\n                }\n            } finally{\n                if (_didIteratorError) {\n                    throw _iteratorError;\n                }\n            }\n        }\n    }, [\n        menu\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().wrapper),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().header),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"text__w--icon align__center add__block\", (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().add__image)),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_13__.Icon, {\n                                variant: \"image\",\n                                type: \"cms\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"collect__body--lg\",\n                                children: \"Add cover image\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 6\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        rows: 1,\n                        placeholder: \"Post Title\",\n                        ref: textareaRef,\n                        className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().headline),\n                        value: headline,\n                        onChange: handleChange\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        \"data-id\": \"0\",\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"text__w--icon align__center add__block\", (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().add__block)),\n                        onClick: triggerMenu,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_13__.Icon, {\n                                variant: \"plus-circle\",\n                                type: \"cms\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"collect__heading--h6\",\n                                children: \"Add block\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 6\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 5\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                lineNumber: 217,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Dnd__WEBPACK_IMPORTED_MODULE_14__.Board, {\n                initial: normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.components,\n                className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().body),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ColumnComponent, {}, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ColumnAddBlock, {}, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 5\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                lineNumber: 240,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ComponentMenu__WEBPACK_IMPORTED_MODULE_15__.ComponentMenu, {\n                trigger: menu,\n                onClose: handleAddBlock\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                lineNumber: 245,\n                columnNumber: 4\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n        lineNumber: 216,\n        columnNumber: 3\n    }, _this);\n};\n_s(LayoutEditor, \"Lwv10dbcrNmFEGn3WObpwHKLwJY=\", false, function() {\n    return [\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_5__.useWindowDimensions,\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__.useIsomorphicLayoutEffect,\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__.useIsomorphicLayoutEffect\n    ];\n});\n_c = LayoutEditor;\nvar _c;\n$RefreshReg$(_c, \"LayoutEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Builder/LayoutEditor/LayoutEditor.tsx\n"));

/***/ })

});