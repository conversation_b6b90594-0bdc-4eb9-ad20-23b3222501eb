"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(pagebuilder)/content-builder/[...slug]/layout",{

/***/ "(app-pages-browser)/./src/layouts/builder/page/LayerSidebarLayout.tsx":
/*!*********************************************************!*\
  !*** ./src/layouts/builder/page/LayerSidebarLayout.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LayerSidebarLayout: function() { return /* binding */ LayerSidebarLayout; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Builder__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/Builder */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/FieldEditor.tsx\");\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pagebuilderlayout.module.scss */ \"(app-pages-browser)/./src/layouts/builder/page/pagebuilderlayout.module.scss\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n// Custom hook để phát hiện click bên ngoài một phần tử\nvar useClickOutside = function(callback) {\n    _s();\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        var handleClickOutside = function(event) {\n            if (ref.current && !ref.current.contains(event.target)) {\n                callback();\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return function() {\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        callback\n    ]);\n    return ref;\n};\n_s(useClickOutside, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\nvar LayerSidebarLayout = function() {\n    _s1();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__.PageBuilderContext);\n    var childCmp = context.childComponentData, setChildComponentData = context.setChildComponentData, layerPos = context.layerPos;\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0), 2), curChildIndex = _useState[0], setCurChildIndex = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), 2), isPrevEntriesOpen = _useState1[0], setIsPrevEntriesOpen = _useState1[1];\n    // Sử dụng hook useClickOutside để đóng dropdown khi click ra ngoài\n    var prevEntriesRef = useClickOutside(function() {\n        if (isPrevEntriesOpen) {\n            setIsPrevEntriesOpen(false);\n        }\n    });\n    var handleBack = function(idx) {\n        var newVal = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__._)(childCmp);\n        console.log(idx);\n        if (idx !== undefined) {\n            if (curChildIndex === idx) return;\n            setCurChildIndex(idx);\n            newVal.splice(idx + 1, newVal.length - (idx + 1));\n        } else {\n            setCurChildIndex(curChildIndex - 1);\n            newVal.pop();\n        }\n        setChildComponentData(newVal);\n    };\n    var handleRemove = function(idx) {\n        console.log(\"Remove idx:\", idx);\n        if (!childCmp[idx]) return;\n        var currentEntry = childCmp[idx];\n        // Use the handleRemove function passed from the Component\n        if (currentEntry.handleRemove && typeof currentEntry.entryIndex === \"number\") {\n            // Call the Component's handleRemove with the correct index\n            currentEntry.handleRemove(currentEntry.entryIndex);\n        }\n        // Update childComponentData - remove current and subsequent entries\n        var newChildData = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__._)(childCmp);\n        newChildData.splice(idx, newChildData.length - idx);\n        setChildComponentData(newChildData);\n        // Update current index to previous level\n        if (idx > 0) {\n            setCurChildIndex(idx - 1);\n        }\n    };\n    var handleDuplicate = function(idx) {\n        console.log(\"Duplicate idx:\", idx);\n        if (!childCmp[idx]) return;\n        var currentEntry = childCmp[idx];\n        // Use the handleDuplicate function passed from the Component\n        if (currentEntry.handleDuplicate && typeof currentEntry.entryIndex === \"number\") {\n            // Call the Component's handleDuplicate with the correct index\n            currentEntry.handleDuplicate(currentEntry.entryIndex);\n        }\n    // Note: Non-repeatable components cannot be duplicated\n    };\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__.useIsomorphicLayoutEffect)(function() {\n        if (!childCmp || childCmp.length === 0) return;\n        setCurChildIndex(childCmp.length - 1);\n    }, [\n        childCmp\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: childCmp && childCmp.length > 0 && childCmp[curChildIndex] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().sidebar), (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().sidebar__layer), layerPos !== \"\" ? (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default())[layerPos] : \"\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__title),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: function() {\n                                return handleBack(undefined);\n                            },\n                            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().back__button),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                type: \"cms\",\n                                variant: \"back\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 8\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 7\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                            className: \"collect__heading collect__heading--h6\",\n                            children: [\n                                childCmp.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries),\n                                            ref: prevEntriesRef,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: function() {\n                                                        return setIsPrevEntriesOpen(!isPrevEntriesOpen);\n                                                    },\n                                                    title: \"Show previous entries\",\n                                                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries__trigger),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 12\n                                                    }, _this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 11\n                                                }, _this),\n                                                isPrevEntriesOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries__list),\n                                                    children: childCmp.map(function(item, idx) {\n                                                        return idx === ( false || curChildIndex) ? null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries__item),\n                                                            title: \"Back to \".concat(item.name),\n                                                            onClick: function() {\n                                                                handleBack(idx);\n                                                                setIsPrevEntriesOpen(false);\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: item.name\n                                                            }, idx, false, {\n                                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 16\n                                                            }, _this)\n                                                        }, idx, false, {\n                                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 15\n                                                        }, _this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 12\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 10\n                                        }, _this),\n                                        \"/\"\n                                    ]\n                                }, void 0, true),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    title: curChildIndex === 0 ? \"\" : \"Back to \".concat(childCmp[curChildIndex].name),\n                                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().cur__entry),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: childCmp[curChildIndex].name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 9\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 8\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 7\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__action),\n                            children: [\n                                childCmp[curChildIndex].entryIndex === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    title: \"Duplicate this entry\",\n                                    onClick: function() {\n                                        return handleDuplicate(curChildIndex);\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                        variant: \"duplicate\",\n                                        type: \"cms\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 11\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 10\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    title: \"Duplicate this entry\",\n                                    onClick: function() {\n                                        return handleDuplicate(curChildIndex);\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                        variant: \"duplicate\",\n                                        type: \"cms\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 9\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 8\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().remove__button),\n                                    title: \"Remove this entry\",\n                                    onClick: function() {\n                                        return handleRemove(curChildIndex);\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                        variant: \"remove\",\n                                        type: \"cms\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 9\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 8\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 7\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 6\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().editor__components),\n                    children: childCmp[curChildIndex].fields.map(function(param) {\n                        var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)(param, 2), key = _param[0], fValue = _param[1];\n                        var _childCmp_curChildIndex_value, _childCmp_curChildIndex;\n                        var fval = fValue;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Builder__WEBPACK_IMPORTED_MODULE_9__.FieldEditor, (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, fval), {\n                            layerPos: layerPos,\n                            name: key,\n                            size: 12,\n                            value: (_childCmp_curChildIndex = childCmp[curChildIndex]) === null || _childCmp_curChildIndex === void 0 ? void 0 : (_childCmp_curChildIndex_value = _childCmp_curChildIndex.value) === null || _childCmp_curChildIndex_value === void 0 ? void 0 : _childCmp_curChildIndex_value[key],\n                            onChange: function(props) {\n                                var _childCmp_curChildIndex;\n                                if (!((_childCmp_curChildIndex = childCmp[curChildIndex]) === null || _childCmp_curChildIndex === void 0 ? void 0 : _childCmp_curChildIndex.onChange)) return;\n                                console.log(props, key, fval, childCmp[curChildIndex].value);\n                                childCmp[curChildIndex].onChange(props);\n                            }\n                        }), key, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 9\n                        }, _this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 6\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n            lineNumber: 102,\n            columnNumber: 5\n        }, _this)\n    }, void 0, false);\n};\n_s1(LayerSidebarLayout, \"HOt8P3zlm4MIAp2I32GX9YJyhhg=\", false, function() {\n    return [\n        useClickOutside,\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__.useIsomorphicLayoutEffect\n    ];\n});\n_c = LayerSidebarLayout;\nvar _c;\n$RefreshReg$(_c, \"LayerSidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/layouts/builder/page/LayerSidebarLayout.tsx\n"));

/***/ })

});